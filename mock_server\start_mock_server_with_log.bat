@echo off
echo [%date% %time%] Starting Mock Server... > D:\01-shuimu_01\logs\server.log
cd /d D:\01-shuimu_01\mock_server
echo [%date% %time%] Working directory: %cd% >> D:\01-shuimu_01\logs\server.log
echo [%date% %time%] Executing: uvicorn src.main:app --host 0.0.0.0 --port 8000 >> D:\01-shuimu_01\logs\server.log
C:\Users\<USER>\anaconda3\Scripts\uvicorn.exe src.main:app --host 0.0.0.0 --port 8000 >> D:\01-shuimu_01\logs\server.log 2>&1 