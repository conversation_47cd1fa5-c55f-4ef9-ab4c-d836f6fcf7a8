# Mock Server 启动脚本 - 带详细日志
param(
    [string]$LogPath = "D:\01-shuimu_01\logs\mock_server_startup.log"
)

# 创建日志目录
$logDir = Split-Path $LogPath -Parent
if (!(Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir -Force
}

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path $LogPath -Value $logMessage
}

# 开始启动
Write-Log "========== Mock Server 启动 =========="
Write-Log "工作目录: D:\01-shuimu_01\mock_server"
Write-Log "日志文件: $LogPath"
Write-Log "命令: uvicorn src.main:app --host 0.0.0.0 --port 8000"

# 切换到工作目录
Set-Location "D:\01-shuimu_01\mock_server"
Write-Log "当前目录: $(Get-Location)"

# 检查 uvicorn 是否存在
$uvicornPath = "C:\Users\<USER>\anaconda3\Scripts\uvicorn.exe"
if (Test-Path $uvicornPath) {
    Write-Log "找到 uvicorn: $uvicornPath"
} else {
    Write-Log "错误: 找不到 uvicorn.exe" "ERROR"
    exit 1
}

# 启动服务
Write-Log "正在启动 Mock Server..."
try {
    # 启动进程并重定向输出
    $process = Start-Process -FilePath $uvicornPath -ArgumentList "src.main:app", "--host", "0.0.0.0", "--port", "8000" -PassThru -RedirectStandardOutput "$LogPath.out" -RedirectStandardError "$LogPath.err" -NoNewWindow
    Write-Log "Mock Server 已启动，PID: $($process.Id)"
    Write-Log "标准输出: $LogPath.out"
    Write-Log "错误输出: $LogPath.err"
    Write-Log "========== 启动完成 =========="
} catch {
    Write-Log "启动失败: $($_.Exception.Message)" "ERROR"
    exit 1
} 