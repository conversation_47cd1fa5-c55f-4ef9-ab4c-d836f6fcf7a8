@echo off
chcp 65001 >nul
set PYTHONIOENCODING=utf-8
set PYTHONLEGACYWINDOWSUTF8MODE=1
set PYTHONUNBUFFERED=1

REM 生成时间戳
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

echo Starting with log recording...

REM 调用服务端API清空日志
echo Requesting server to clear logs via API...
curl -X POST -s http://localhost:8000/api/admin/v1/logs/clear 2>nul
if %ERRORLEVEL% equ 0 (
    echo Server logs cleared successfully via API
) else (
    echo Server log clear API request failed or server not running
)

echo Creating log directory...
if not exist "D:\01-shuimu_01\logs" mkdir "D:\01-shuimu_01\logs"

echo Activating virtual environment...
call venv\Scripts\activate.bat
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

echo Virtual environment activated
echo Checking Python...
python --version
if %ERRORLEVEL% neq 0 (
    echo ERROR: Python not found
    pause
    exit /b 1
)

echo Starting Python application...
echo [%date% %time%] === NEW ADMIN SESSION START === > "D:\01-shuimu_01\logs\admin.log"
python -u src\main.py >> "D:\01-shuimu_01\logs\admin.log" 2>&1

echo Admin log saved to: D:\01-shuimu_01\logs\admin.log
pause 