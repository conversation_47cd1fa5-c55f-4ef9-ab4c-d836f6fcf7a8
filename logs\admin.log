[2025-07-20 10:20:17.81] === NEW ADMIN SESSION START === 
✅ 数据库管理器初始化完成
✅ UUID生成器初始化完成 - 无需映射机制
✅ 并行批量操作管理器已加载
✅ 同步工作线程集成补偿处理器
✅ 线程管理：已启动2个工作线程（CRUD+同步），符合规则v4.0
✅ 工作线程信号连接完成
✅ UI信号连接跳过（由外部调用者处理）
✅ 主窗口乐观更新功能初始化成功
🚀 CRUD工作线程启动🚀 同步工作线程启动

✅ 内容区域清理完成
🚀 开始初始化课程数据...
📡 API模式: True (强制使用服务器数据，确保数据一致性)
✅ 新的数据同步管理器初始化成功
🔄 异步同步后台线程已启动
✅ CourseService API导入成功: API_AVAILABLE = True
🔧 CourseService初始化:
   use_api参数: True
   API_AVAILABLE: True
   最终use_api: True
   use_cache: True
✅ 同步管理器API客户端已设置
🚀 开始一次性加载所有数据到内存...
📂 获取所有分类数据（无分页）...
🔍 API基础URL: http://localhost:8000
🔍 API响应类型: <class 'dict'>
✅ 获取所有分类完成: 46 个
📹 获取所有视频数据（无分页）...
🔍 API基础URL: http://localhost:8000
🔍 API响应类型: <class 'dict'>
✅ 获取所有视频完成: 67 个
🔍 预加载用户详情进度: 1/7
API请求失败: GET http://localhost:8000/api/admin/v1/users/test-user-purchase-001/progress -> HTTP 500: Internal Server Error
API请求失败: GET http://localhost:8000/api/admin/v1/users/test-user-purchase-001/favorites -> HTTP 500: Internal Server Error
API请求失败: GET http://localhost:8000/api/admin/v1/users/test-user-purchase-001/cache -> HTTP 500: Internal Server Error
API请求失败: GET http://localhost:8000/api/admin/v1/users/test-api-001/progress -> HTTP 500: Internal Server Error
API请求失败: GET http://localhost:8000/api/admin/v1/users/test-api-001/favorites -> HTTP 500: Internal Server Error
API请求失败: GET http://localhost:8000/api/admin/v1/users/test-api-001/cache -> HTTP 500: Internal Server Error
API请求失败: GET http://localhost:8000/api/admin/v1/users/test-plaintext-001/progress -> HTTP 500: Internal Server Error
API请求失败: GET http://localhost:8000/api/admin/v1/users/test-plaintext-001/favorites -> HTTP 500: Internal Server Error
