#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理API - 用于执行数据库结构更新
"""

import os
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from ..database.mysql_manager import mysql_manager

router = APIRouter()

class SQLRequest(BaseModel):
    sql: str

class TableRequest(BaseModel):
    table_name: str
    
class ColumnRequest(BaseModel):
    table_name: str
    column_name: str
    column_definition: str

class IndexRequest(BaseModel):
    table_name: str
    index_name: str
    columns: str

# ==================== 日志管理API ====================

@router.post("/logs/clear")
def clear_server_logs():
    """清空服务端日志文件"""
    try:
        log_file_path = "D:/01-shuimu_01/logs/server.log"
        
        # 检查文件是否存在
        if os.path.exists(log_file_path):
            # 清空文件内容而不是删除文件（避免文件占用问题）
            with open(log_file_path, 'w', encoding='utf-8') as f:
                f.write("")
            
            return {
                "success": True,
                "message": "服务端日志已清空",
                "log_file": log_file_path,
                "timestamp": "2025-07-20"
            }
        else:
            return {
                "success": True,
                "message": "日志文件不存在，无需清空",
                "log_file": log_file_path
            }
            
    except PermissionError:
        raise HTTPException(
            status_code=500, 
            detail="无法清空日志文件：文件正在被其他进程使用"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"清空日志失败: {str(e)}"
        )

@router.get("/logs/status")
def get_log_status():
    """获取日志文件状态"""
    try:
        log_file_path = "D:/01-shuimu_01/logs/server.log"
        
        if os.path.exists(log_file_path):
            file_size = os.path.getsize(log_file_path)
            
            # 读取文件行数
            try:
                with open(log_file_path, 'r', encoding='utf-8') as f:
                    line_count = sum(1 for line in f)
            except:
                line_count = 0
                
            return {
                "success": True,
                "log_file": log_file_path,
                "exists": True,
                "size_bytes": file_size,
                "size_kb": round(file_size / 1024, 2),
                "line_count": line_count,
                "can_clear": True
            }
        else:
            return {
                "success": True,
                "log_file": log_file_path,
                "exists": False,
                "size_bytes": 0,
                "size_kb": 0,
                "line_count": 0,
                "can_clear": False
            }
            
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"获取日志状态失败: {str(e)}"
        )

# ==================== 原有的数据库管理API ====================

@router.get("/database/status")
def get_database_status():
    """获取数据库连接状态"""
    try:
        # 测试数据库连接
        result = mysql_manager.execute_query("SELECT 1 as test;")
        if result:
            return {
                "success": True,
                "message": "数据库连接正常",
                "status": "connected"
            }
        else:
            raise HTTPException(status_code=500, detail="数据库查询返回空结果")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库连接失败: {str(e)}")

@router.post("/database/execute-sql")
def execute_sql(request: SQLRequest):
    """执行SQL语句 - 支持完整的数据库管理操作"""
    try:
        sql = request.sql.strip()
        sql_upper = sql.upper()
        
        # 允许的操作类型
        allowed_operations = [
            'ALTER TABLE', 'CREATE TABLE', 'DROP TABLE',
            'CREATE INDEX', 'DROP INDEX', 
            'INSERT', 'UPDATE', 'DELETE',
            'SHOW', 'DESCRIBE', 'SELECT'
        ]
        
        # 检查是否为允许的操作
        is_allowed = any(sql_upper.startswith(op) for op in allowed_operations)
        if not is_allowed:
            raise HTTPException(status_code=400, detail="不支持的SQL操作类型")
        
        # 特别危险的操作需要额外检查
        if 'DROP TABLE' in sql_upper:
            # DROP TABLE需要明确指定表名，不允许通配符
            if '*' in sql or '%' in sql:
                raise HTTPException(status_code=400, detail="DROP TABLE不允许使用通配符")
        
        # 执行SQL
        if sql_upper.startswith(('SELECT', 'SHOW', 'DESCRIBE')):
            result = mysql_manager.execute_query(request.sql)
            return {
                "success": True,
                "message": "查询执行成功",
                "data": result
            }
        else:
            result = mysql_manager.execute_update(request.sql)
            return {
                "success": True,
                "message": "SQL执行成功",
                "affected_rows": result
            }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"SQL执行失败: {str(e)}")

@router.post("/database/backup")
def backup_database():
    """备份数据库"""
    try:
        # 这里应该实现实际的备份逻辑
        # 暂时返回模拟结果
        return {
            "success": True,
            "message": "数据库备份启动",
            "backup_id": "backup_20250720_100000",
            "status": "in_progress"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"备份失败: {str(e)}")

# =============================================================================
# 完整的数据库管理API端点
# =============================================================================

@router.post("/database/create-table")
def create_table(request: SQLRequest):
    """创建新表"""
    try:
        sql = request.sql.strip()
        if not sql.upper().startswith('CREATE TABLE'):
            raise HTTPException(status_code=400, detail="必须是CREATE TABLE语句")
        
        mysql_manager.execute_update(sql)
        return {
            "success": True,
            "message": "表创建成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建表失败: {str(e)}")

@router.post("/database/alter-table")
def alter_table(request: SQLRequest):
    """修改表结构"""
    try:
        sql = request.sql.strip()
        if not sql.upper().startswith('ALTER TABLE'):
            raise HTTPException(status_code=400, detail="必须是ALTER TABLE语句")
        
        mysql_manager.execute_update(sql)
        return {
            "success": True,
            "message": "表结构修改成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"修改表结构失败: {str(e)}")

@router.get("/database/table-schema/{table_name}")
def get_table_schema(table_name: str):
    """获取表结构"""
    try:
        result = mysql_manager.execute_query(f"DESCRIBE {table_name};")
        return {
            "success": True,
            "table_name": table_name,
            "schema": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取表结构失败: {str(e)}")

@router.post("/database/create-index")
def create_index(request: IndexRequest):
    """创建索引"""
    try:
        sql = f"CREATE INDEX {request.index_name} ON {request.table_name} ({request.columns});"
        mysql_manager.execute_update(sql)
        return {
            "success": True,
            "message": f"索引 {request.index_name} 创建成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建索引失败: {str(e)}")

@router.delete("/database/drop-index/{table_name}/{index_name}")
def drop_index(table_name: str, index_name: str):
    """删除索引"""
    try:
        sql = f"DROP INDEX {index_name} ON {table_name};"
        mysql_manager.execute_update(sql)
        return {
            "success": True,
            "message": f"索引 {index_name} 删除成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除索引失败: {str(e)}")

@router.get("/database/list-tables")
def list_tables():
    """获取所有表列表"""
    try:
        result = mysql_manager.execute_query("SHOW TABLES;")
        tables = [list(row.values())[0] for row in result]
        return {
            "success": True,
            "tables": tables
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取表列表失败: {str(e)}")

@router.get("/database/table-data/{table_name}")
def get_table_data(table_name: str, limit: int = 100, offset: int = 0):
    """获取表数据"""
    try:
        result = mysql_manager.execute_query(f"SELECT * FROM {table_name} LIMIT {limit} OFFSET {offset};")
        return {
            "success": True,
            "table_name": table_name,
            "data": result,
            "limit": limit,
            "offset": offset
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取表数据失败: {str(e)}")

@router.get("/database/table-indexes/{table_name}")
def get_table_indexes(table_name: str):
    """获取指定表的索引"""
    try:
        result = mysql_manager.execute_query(f"SHOW INDEX FROM {table_name};")
        return {
            "success": True,
            "table_name": table_name,
            "indexes": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取表索引失败: {str(e)}")

@router.get("/database/database-info")
def get_database_info():
    """获取数据库基本信息"""
    try:
        # 获取数据库版本
        version_result = mysql_manager.execute_query("SELECT VERSION() as version;")
        version = version_result[0]['version'] if version_result else "Unknown"
        
        # 获取所有表
        tables_result = mysql_manager.execute_query("SHOW TABLES;")
        table_count = len(tables_result)
        
        return {
            "success": True,
            "database_version": version,
            "table_count": table_count,
            "created_at": "2025-07-01"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库信息失败: {str(e)}")

@router.post("/database/optimize")
def optimize_database():
    """优化数据库"""
    try:
        # 获取所有表
        tables_result = mysql_manager.execute_query("SHOW TABLES;")
        tables = [list(row.values())[0] for row in tables_result]
        
        optimized_tables = []
        for table in tables:
            try:
                mysql_manager.execute_update(f"OPTIMIZE TABLE {table};")
                optimized_tables.append(table)
            except Exception as e:
                print(f"优化表 {table} 失败: {e}")
        
        return {
            "success": True,
            "message": f"数据库优化完成，优化了 {len(optimized_tables)} 个表",
            "optimized_tables": optimized_tables
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库优化失败: {str(e)}")

# ==================== 测试端点 ====================

@router.get("/test/connection")
def test_database_connection():
    """测试数据库连接"""
    try:
        result = mysql_manager.execute_query("SELECT NOW() as current_time, USER() as current_user;")
        return {
            "success": True,
            "message": "数据库连接测试成功",
            "data": result[0] if result else None
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库连接测试失败: {str(e)}")

@router.post("/test/create-sample-data")
def create_sample_data():
    """创建示例数据（测试用）"""
    try:
        # 这里可以添加创建示例数据的逻辑
        return {
            "success": True,
            "message": "示例数据创建功能准备就绪",
            "note": "请在生产环境中谨慎使用"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建示例数据失败: {str(e)}")