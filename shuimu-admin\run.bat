@echo off
chcp 65001 >nul
set PYTHONIOENCODING=utf-8
set PYTHONLEGACYWINDOWSUTF8MODE=1

echo Starting Shuimu Course Admin...

REM 调用服务端API清空日志
echo Requesting server to clear logs via API...
curl -X POST -s http://localhost:8000/api/admin/v1/logs/clear 2>nul
if %ERRORLEVEL% equ 0 (
    echo Server logs cleared successfully via API
) else (
    echo Server log clear API request failed or server not running
)

echo Activating virtual environment...
call venv\Scripts\activate.bat
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

echo Virtual environment activated successfully
echo Starting application...
python src\main.py
pause
